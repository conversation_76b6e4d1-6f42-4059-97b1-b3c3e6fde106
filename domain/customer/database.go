package customer

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
)

// getList fetches all customers ordered by name.
func (rsc CustomerResource) getList(ctx context.Context) ([]Customer, error) {
	var customers []Customer

	db := dbmanager.Manager().WithContext(ctx)

	// Exclude soft deleted records and order by name ascending
	err := db.Where("deleted_at IS NULL").
		Order("name ASC").
		Find(&customers).Error

	if err != nil {
		return []Customer{}, log.LogError(err, nil)
	}

	return customers, nil
}
