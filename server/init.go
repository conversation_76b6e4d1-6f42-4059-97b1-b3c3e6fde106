package server

import (
	customerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	healthDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/health"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	userRoleDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user_role"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	customerusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
	healthusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/health"
	sitereportusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
	userusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/user"
)

var (
	// usecases
	HealthUseCase     *healthusecase.HealthUseCase
	UserUseCase       *userusecase.UserUseCase
	SiteReportUseCase *sitereportusecase.SiteReportUseCase
	CustomerUseCase   *customerusecase.CustomerUseCase

	// domains
	healthDomain     healthDmn.HealthDomain
	userDomain       userDmn.UserDomain
	userRoleDomain   userRoleDmn.UserRoleDomain
	siteReportDomain sitereportDmn.SiteReportDomain
	customerDomain   customerDmn.CustomerDomain
)

func Init(mode ...string) error {
	config.InitDatabase()
	config.InitGoAdmin()
	InitAccountingSystem()

	return nil
}

func InitAccountingSystem() {
	healthDomain = healthDmn.InitHealthDomain(healthDmn.HealthResource{})
	userDomain = userDmn.InitUserDomain(userDmn.UserResource{})
	userRoleDomain = userRoleDmn.InitUserRoleDomain(userRoleDmn.UserRoleResource{})
	siteReportDomain = sitereportDmn.InitSiteReportDomain(sitereportDmn.SiteReportResource{})
	customerDomain = customerDmn.InitCustomerDomain(customerDmn.CustomerResource{})

	healthDomains := healthusecase.Domains{
		HealthDomain: &healthDomain,
	}

	userDomains := userusecase.Domains{
		UserDomain:     &userDomain,
		UserRoleDomain: &userRoleDomain,
	}

	siteReportDomains := sitereportusecase.Domains{
		SiteReportDomain: &siteReportDomain,
	}

	customerDomains := customerusecase.Domains{
		CustomerDomain: &customerDomain,
	}

	HealthUseCase = healthusecase.InitHealthUseCase(healthDomains)
	UserUseCase = userusecase.InitUserUseCase(userDomains)
	SiteReportUseCase = sitereportusecase.InitSiteReportUseCase(siteReportDomains)
	CustomerUseCase = customerusecase.InitCustomerUseCase(customerDomains)
}
