package customer

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	CustomerDomainItf interface {
		GetList(ctx context.Context) ([]Customer, error)
	}

	CustomerResourceItf interface {
		getList(ctx context.Context) ([]Customer, error)
	}
)

// GetList retrieves all customers ordered by name.
func (d *CustomerDomain) GetList(ctx context.Context) ([]Customer, error) {
	customers, err := d.resource.getList(ctx)
	if err != nil {
		return []Customer{}, log.LogError(err, nil)
	}
	return customers, nil
}
