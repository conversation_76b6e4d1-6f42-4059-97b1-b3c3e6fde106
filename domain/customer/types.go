package customer

import "time"

type Customer struct {
	ID                int64      `gorm:"column:id;primary_key"`
	Code              string     `gorm:"column:code;unique"`
	Name              string     `gorm:"column:name"`
	Furigana          string     `gorm:"column:furigana"`
	PostCode          string     `gorm:"column:post_code"`
	AddressPrefecture string     `gorm:"column:address_prefecture"`
	AddressCity       string     `gorm:"column:address_city"`
	AddressBuilding   string     `gorm:"column:address_building"`
	Telephone         string     `gorm:"column:telephone"`
	Fax               string     `gorm:"column:fax"`
	BillingDate       time.Time  `gorm:"column:billing_date"`
	StatutoryID       int64      `gorm:"column:statutory_id"`
	CreatedAt         time.Time  `gorm:"column:created_at"`
	UpdatedAt         time.Time  `gorm:"column:updated_at"`
	DeletedAt         *time.Time `gorm:"column:deleted_at"`

	// TODO will uncomment when related table model is created
	// Statutory statutory.Statutory `gorm:"foreignkey:StatutoryID"`
}
