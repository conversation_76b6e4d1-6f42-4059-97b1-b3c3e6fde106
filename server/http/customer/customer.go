package customer

import (
	"net/http"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	"github.com/labstack/echo/v4"
)

func GetList(c echo.Context) error {
	var status int

	respData, err := server.CustomerUseCase.GetList(c.Request().Context())
	if err != nil {
		status = http.StatusInternalServerError
		return c.<PERSON>SON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[customer.GetList]", map[string]interface{}{"resp:": resp})

	return c.<PERSON>(status, resp)
}
