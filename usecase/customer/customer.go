package customer

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

func (uc *CustomerUseCase) GetList(ctx context.Context) ([]GetListResp, error) {
	// Get customers from domain
	customers, err := uc.customer.GetList(ctx)
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}

	// Convert to response format
	var resp []GetListResp
	for _, customer := range customers {
		resp = append(resp, GetListResp{
			ID:   customer.ID,
			Name: customer.Name,
		})
	}

	return resp, nil
}
