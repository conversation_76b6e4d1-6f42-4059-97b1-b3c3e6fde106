package department

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
)

type Department struct {
	ID                    int64      `gorm:"column:id;primary_key"`
	Code                  string     `gorm:"column:code;unique"`
	CustomerID            int64      `gorm:"column:customer_id"`
	Name                  string     `gorm:"column:name"`
	TransferDestinationID int64      `gorm:"column:transfer_destination_id"`
	CreatedAt             time.Time  `gorm:"column:created_at"`
	UpdatedAt             time.Time  `gorm:"column:updated_at"`
	DeletedAt             *time.Time `gorm:"column:deleted_at"`

	// TODO will uncomment when related table model is created
	Customer customer.Customer `gorm:"foreignkey:CustomerID"`
	// TransferDestination transfer_destination.TransferDestination `gorm:"foreignkey:TransferDestinationID"`
}
